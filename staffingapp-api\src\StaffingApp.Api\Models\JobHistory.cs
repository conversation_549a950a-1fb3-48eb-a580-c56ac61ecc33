namespace StaffingApp.Api.Models;

public class JobHistory
{
    public int Id { get; set; }
    public int JobId { get; set; }
    public Job Job { get; set; } = null!;
    
    public JobHistoryAction Action { get; set; }
    public string? PreviousStatus { get; set; }
    public string? NewStatus { get; set; }
    public string? Details { get; set; }
    public decimal? HoursSpent { get; set; }
    
    public int UserId { get; set; }
    public User User { get; set; } = null!;
    
    public DateTime CreatedAt { get; set; }
    
    // Additional context fields
    public string? Notes { get; set; }
    public string? OutputDetails { get; set; }
    public int? AssignedToId { get; set; }
    public User? AssignedTo { get; set; }
}

public enum JobHistoryAction
{
    Created,
    Assigned,
    Started,
    Completed,
    Reviewed,
    Released,
    Satisfied,
    Returned,
    TimeLogged,
    CommentAdded,
    StatusChanged
}
