export interface HoursStatistics {
    hoursBought: number;
    hoursUtilized: number;
    hoursAvailable: number;
}

export interface Job {
    id: number;
    title: string;
    description: string;
    jobType: string;
    category: string;
    outputFormat: string;
    status: string;
    attachmentUrl?: string;
    referenceUrl?: string;
    clientId: number;
    createdAt: string;
    updatedAt?: string;
    assignedToId?: number;
    assignedTo?: User;
    reviewedById?: number;
    reviewedBy?: User;
    assignedAt?: string;
    reviewedAt?: string;
    supervisorNotes?: string;
    outputDetails?: string;
    completedAt?: string;
    deliveredAt?: string;
    satisfiedAt?: string;
    comments?: JobComment[];
}

export interface User {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    clientId: number;
    role: string;
}

export enum JobType {
    DataEntry = 'DataEntry',
    Accounting = 'Accounting',
    HR = 'HR',
    ITSupport = 'ITSupport',
    Marketing = 'Marketing',
    Legal = 'Legal',
    CustomerService = 'CustomerService',
    Other = 'Other'
}

export interface AccountStatementEntry {
    date: string;
    type: string;
    reference: string;
    transaction: string;
    hoursBought: number;
    hoursSpent: number;
    adjustment: number;
    balanceHours: number;
    amount: number;
    balanceAmount: number;
}

export interface AccountStatement {
    entries: AccountStatementEntry[];
    totalHoursBought: number;
    totalHoursSpent: number;
    currentHoursBalance: number;
    totalAmountSpent: number;
    currentAmountBalance: number;
}

export enum JobCategory {
    // Data Entry Categories
    DataProcessing = 'DataProcessing',
    DataCleaning = 'DataCleaning',
    DocumentationEntry = 'DocumentationEntry',

    // Accounting Categories
    Bookkeeping = 'Bookkeeping',
    FinancialReporting = 'FinancialReporting',
    Taxation = 'Taxation',
    Payroll = 'Payroll',

    // HR Categories
    Recruitment = 'Recruitment',
    EmployeeRelations = 'EmployeeRelations',
    Training = 'Training',
    CompensationBenefits = 'CompensationBenefits',

    // IT Support Categories
    TechnicalSupport = 'TechnicalSupport',
    NetworkSupport = 'NetworkSupport',
    SoftwareSupport = 'SoftwareSupport',
    HardwareSupport = 'HardwareSupport',

    // Marketing Categories
    DigitalMarketing = 'DigitalMarketing',
    ContentCreation = 'ContentCreation',
    SocialMedia = 'SocialMedia',
    MarketResearch = 'MarketResearch',

    // Legal Categories
    ContractReview = 'ContractReview',
    Compliance = 'Compliance',
    LegalResearch = 'LegalResearch',
    Documentation = 'Documentation',

    // Customer Service Categories
    CallCenter = 'CallCenter',
    EmailSupport = 'EmailSupport',
    ChatSupport = 'ChatSupport',
    CustomerFeedback = 'CustomerFeedback',

    // Other
    Other = 'Other'
}

export enum OutputFormat {
    PDF = 'PDF',
    Word = 'Word',
    Excel = 'Excel',
    PlainText = 'PlainText',
    JSON = 'JSON',
    XML = 'XML',
    Database = 'Database',
    Other = 'Other'
}

export enum JobStatus {
    New = 'New',
    Returned = 'Returned',
    Assigned = 'Assigned',
    InProgress = 'In Progress',
    AwaitingReview = 'Awaiting Review',
    Released = 'Released',
    Closed = 'Closed'
}

export enum UserRole {
    User = 'User',
    Admin = 'Admin',
    Supervisor = 'Supervisor',
    Staff = 'Staff'
}

export interface CreateJobRequest {
    title: string;
    description: string;
    jobType: JobType;
    category: JobCategory;
    outputFormat: OutputFormat;
    attachmentUrl?: string;
    referenceUrl?: string;
    clientId: number;
}

export interface JobComment {
    id: number;
    content: string;
    createdAt: string;
    user: User;
}
