'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Job, UserRole } from '@/types/models';
import { getJobById, addJobComment, reviewCompletedJob } from '@/lib/job';
import { getRoleBasedStatusDisplay, getStatusColors } from '@/utils/statusDisplay';

export default function SupervisorJobDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const [job, setJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);

  // Comment state
  const [newComment, setNewComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);

  // Workflow state
  const [isSubmitting, setIsSubmitting] = useState(false);

  const jobId = params.id ? parseInt(params.id as string) : null;

  useEffect(() => {
    if (!jobId) {
      setError('Invalid job ID');
      setIsLoading(false);
      return;
    }

    // Get user info
    const userStr = sessionStorage.getItem('user');
    if (userStr) {
      const userData = JSON.parse(userStr);
      if (userData.role !== 'Supervisor') {
        router.push('/dashboard');
        return;
      }
      setUser(userData);
    }

    const fetchJob = async () => {
      try {
        console.log('SupervisorJobDetailsPage: Fetching job with ID:', jobId);
        const jobData = await getJobById(jobId);
        console.log('SupervisorJobDetailsPage: Job fetched successfully:', jobData);
        setJob(jobData);
      } catch (err) {
        console.error('SupervisorJobDetailsPage: Error fetching job:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch job details';
        setError(errorMessage);

        if (errorMessage.includes('Authentication required') || errorMessage.includes('401')) {
          router.push('/login');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchJob();
  }, [jobId, router]);

  const handleAddComment = async () => {
    if (!job || !newComment.trim()) return;

    setIsSubmittingComment(true);
    try {
      await addJobComment(job.id, newComment.trim());
      // Refresh job data
      const updatedJob = await getJobById(job.id);
      setJob(updatedJob);
      setNewComment('');
    } catch (err) {
      console.error('Error adding comment:', err);
      setError(err instanceof Error ? err.message : 'Failed to add comment');
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const handleReviewJob = async () => {
    if (!job) return;

    setIsSubmitting(true);
    try {
      await reviewCompletedJob(job.id);
      // Refresh job data
      const updatedJob = await getJobById(job.id);
      setJob(updatedJob);
    } catch (err) {
      console.error('Error reviewing job:', err);
      setError(err instanceof Error ? err.message : 'Failed to review job');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const colors = getStatusColors(status);
    const displayStatus = getRoleBasedStatusDisplay(status, UserRole.Supervisor);

    return (
      <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${colors}`}>
        {displayStatus}
      </span>
    );
  };

  // Determine available actions based on job status
  const getAvailableActions = () => {
    if (!job || !user) return [];

    const actions = [];

    // Supervisor actions
    if (user.role === 'Supervisor' && job.status === 'AwaitingReview') {
      actions.push({
        type: 'review',
        label: 'Review & Deliver',
        color: 'emerald',
        action: handleReviewJob
      });
    }

    return actions;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="fixed top-0 left-0 right-0 bg-white shadow-sm z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900">Staff Hall</h1>
              </div>
              <nav className="flex space-x-8">
                <button
                  onClick={() => router.push('/supervisor')}
                  className="text-gray-600 hover:text-emerald-600 px-3 py-2 text-sm font-medium"
                >
                  Dashboard
                </button>
                <button
                  onClick={() => {
                    sessionStorage.removeItem('token');
                    sessionStorage.removeItem('user');
                    router.push('/login');
                  }}
                  className="text-gray-600 hover:text-emerald-600 px-3 py-2 text-sm font-medium"
                >
                  Logout
                </button>
              </nav>
            </div>
          </div>
        </div>

        <main className="pt-24 py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading job details...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="fixed top-0 left-0 right-0 bg-white shadow-sm z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900">Staff Hall</h1>
              </div>
              <nav className="flex space-x-8">
                <button
                  onClick={() => router.push('/supervisor')}
                  className="text-gray-600 hover:text-emerald-600 px-3 py-2 text-sm font-medium"
                >
                  Dashboard
                </button>
                <button
                  onClick={() => {
                    sessionStorage.removeItem('token');
                    sessionStorage.removeItem('user');
                    router.push('/login');
                  }}
                  className="text-gray-600 hover:text-emerald-600 px-3 py-2 text-sm font-medium"
                >
                  Logout
                </button>
              </nav>
            </div>
          </div>
        </div>

        <main className="pt-24 py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen bg-gray-50">
        <main className="pt-24 py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p className="text-center text-gray-600">Job not found.</p>
          </div>
        </main>
      </div>
    );
  }

  const availableActions = getAvailableActions();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 bg-white shadow-sm z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Staff Hall</h1>
            </div>
            <nav className="flex space-x-8">
              <button
                onClick={() => router.push('/supervisor')}
                className="text-gray-600 hover:text-emerald-600 px-3 py-2 text-sm font-medium"
              >
                Dashboard
              </button>
              <button
                onClick={() => {
                  sessionStorage.removeItem('token');
                  sessionStorage.removeItem('user');
                  router.push('/login');
                }}
                className="text-gray-600 hover:text-emerald-600 px-3 py-2 text-sm font-medium"
              >
                Logout
              </button>
            </nav>
          </div>
        </div>
      </div>

      <main className="pt-24 py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/supervisor')}
              className="mb-4 text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Supervisor Dashboard
            </button>
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold text-gray-900">{job.title}</h1>
              {getStatusBadge(job.status)}
            </div>
          </div>

          {/* Action Buttons */}
          {availableActions.length > 0 && (
            <div className="mb-6">
              <div className="flex space-x-4">
                {availableActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={action.action}
                    disabled={isSubmitting}
                    className={`bg-${action.color}-600 hover:bg-${action.color}-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium transition-colors`}
                  >
                    {isSubmitting ? 'Processing...' : action.label}
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Job Details */}
            <div className="lg:col-span-2">
              <div className="bg-white shadow rounded-lg p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Job Details</h2>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{job.description}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Job Type</label>
                      <p className="mt-1 text-sm text-gray-900">{job.jobType}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Category</label>
                      <p className="mt-1 text-sm text-gray-900">{job.category}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Output Format</label>
                      <p className="mt-1 text-sm text-gray-900">{job.outputFormat}</p>
                    </div>
                  </div>

                  {job.referenceUrl && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Reference URL</label>
                      <a
                        href={job.referenceUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="mt-1 text-sm text-emerald-600 hover:text-emerald-800"
                      >
                        {job.referenceUrl}
                      </a>
                    </div>
                  )}

                  {job.attachmentUrl && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Attachment</label>
                      <a
                        href={job.attachmentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="mt-1 text-sm text-emerald-600 hover:text-emerald-800"
                      >
                        View Attachment
                      </a>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(job.createdAt)}</p>
                  </div>
                </div>
              </div>

              {/* Assignment Information */}
              {(job.assignedTo || job.supervisorNotes) && (
                <div className="bg-white shadow rounded-lg p-6 mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Assignment Information</h2>

                  <div className="space-y-4">
                    {job.assignedTo && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Assigned To</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {job.assignedTo.firstName} {job.assignedTo.lastName} ({job.assignedTo.email})
                        </p>
                      </div>
                    )}

                    {job.assignedAt && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Assigned At</label>
                        <p className="mt-1 text-sm text-gray-900">{formatDate(job.assignedAt)}</p>
                      </div>
                    )}

                    {job.supervisorNotes && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Supervisor Notes</label>
                        <p className="mt-1 text-sm text-gray-900">{job.supervisorNotes}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Output Details */}
              {job.outputDetails && (
                <div className="bg-white shadow rounded-lg p-6 mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Output Details</h2>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Staff Output Description</label>
                      <div className="mt-1 p-3 bg-blue-50 rounded-md">
                        <p className="text-sm text-blue-800">{job.outputDetails}</p>
                      </div>
                    </div>

                    {job.completedAt && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Completed At</label>
                        <p className="mt-1 text-sm text-gray-900">{formatDate(job.completedAt)}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Comments Section */}
            <div className="lg:col-span-1">
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Comments</h2>

                {/* Comments List */}
                <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
                  {job.comments && job.comments.length > 0 ? (
                    job.comments.map((comment) => (
                      <div key={comment.id} className="border-l-4 border-emerald-200 pl-4 py-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-900">
                            {comment.user.firstName} {comment.user.lastName}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatDate(comment.createdAt)}
                          </span>
                        </div>
                        <div className="flex items-center mb-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            comment.user.role === 'Supervisor'
                              ? 'bg-purple-100 text-purple-800'
                              : comment.user.role === 'Staff'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {comment.user.role}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700">{comment.content}</p>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500 italic">No comments yet.</p>
                  )}
                </div>

                {/* Add Comment Form */}
                <div className="border-t pt-4">
                  <div className="space-y-3">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Add a comment..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
                    />
                    <button
                      onClick={handleAddComment}
                      disabled={!newComment.trim() || isSubmittingComment}
                      className="w-full bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                      {isSubmittingComment ? 'Adding...' : 'Add Comment'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}