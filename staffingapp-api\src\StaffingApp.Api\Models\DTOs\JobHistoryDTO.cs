using StaffingApp.Api.Models;

namespace StaffingApp.Api.Models.DTOs
{
    public class JobHistoryDTO
    {
        public int Id { get; set; }
        public int JobId { get; set; }
        public JobHistoryAction Action { get; set; }
        public string? PreviousStatus { get; set; }
        public string? NewStatus { get; set; }
        public string? Details { get; set; }
        public decimal? HoursSpent { get; set; }
        public UserDTO User { get; set; } = null!;
        public DateTime CreatedAt { get; set; }
        public string? Notes { get; set; }
        public string? OutputDetails { get; set; }
        public UserDTO? AssignedTo { get; set; }
    }

    public class LogTimeRequest
    {
        public decimal HoursSpent { get; set; }
        public string? Description { get; set; }
    }

    public class ReviewJobRequest
    {
        public bool Approved { get; set; }
        public string? Notes { get; set; }
        public string? OutputFormatFeedback { get; set; }
        public bool OutputFormatAcceptable { get; set; } = true;
        public string? RequiredChanges { get; set; }
    }

    public class TimeEntryApprovalRequest
    {
        public bool Approved { get; set; }
        public string? Notes { get; set; }
    }
}
