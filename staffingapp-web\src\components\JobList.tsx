'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Job, HoursStatistics } from '@/types/models';
import { getJobs } from '@/lib/job';
import { getRoleBasedStatusDisplay, getStatusColors } from '@/utils/statusDisplay';

// Utility function to format enum names with proper spacing
const formatEnumName = (enumValue: string): string => {
  return enumValue.replace(/([A-Z])/g, ' $1').trim();
};

interface JobListProps {
  onError?: (error: string) => void;
  hoursStatistics?: HoursStatistics;
}

export default function JobList({ onError, hoursStatistics }: JobListProps) {
  const router = useRouter();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const fetchJobs = async () => {
      try {
        console.log('JobList: Starting to fetch jobs...');
        setIsLoading(true);
        setError(null);

        // Check if we're in the browser before making API calls
        if (typeof window === 'undefined') {
          console.log('JobList: Not in browser, stopping');
          setIsLoading(false);
          return;
        }

        // Add delay to ensure authentication is ready
        console.log('JobList: Waiting for authentication to be ready...');
        await new Promise(resolve => setTimeout(resolve, 200));

        // Debug: Check authentication state
        const token = sessionStorage.getItem('token');
        const user = sessionStorage.getItem('user');
        console.log('JobList: Auth check - Token exists:', !!token, 'User exists:', !!user);
        console.log('JobList: SessionStorage length:', sessionStorage.length);

        if (token) {
          console.log('JobList: Token preview:', token.substring(0, 20) + '...');
          console.log('JobList: Token length:', token.length);
        } else {
          console.log('JobList: No token found in sessionStorage');
          console.log('JobList: All sessionStorage keys:', Object.keys(sessionStorage));

          // Try to get token again after a short delay
          await new Promise(resolve => setTimeout(resolve, 500));
          const retryToken = sessionStorage.getItem('token');
          console.log('JobList: Retry token check:', !!retryToken);

          if (!retryToken) {
            console.log('JobList: Still no token after retry, user might not be logged in');
            setError('Please log in to view jobs');
            setIsLoading(false);
            return;
          }
        }

        console.log('JobList: Calling getJobs()...');
        const jobsData = await getJobs();
        console.log('JobList: Jobs fetched successfully:', jobsData);
        console.log('JobList: Number of jobs:', jobsData.length);
        setJobs(jobsData);
      } catch (err) {
        console.error('JobList: Error fetching jobs:', err);
        console.error('JobList: Error details:', {
          message: err instanceof Error ? err.message : 'Unknown error',
          stack: err instanceof Error ? err.stack : undefined
        });
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch jobs';
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobs();
  }, [mounted, onError]);

  if (!mounted) {
    return (
      <div style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        borderRadius: '8px',
        padding: '24px'
      }}>
        <h3 style={{fontSize: '18px', fontWeight: '500', margin: '0 0 16px 0'}}>Work History</h3>
        <p style={{color: '#6b7280', margin: '0'}}>Loading...</p>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const user = JSON.parse(sessionStorage.getItem('user') || '{}');
    const userRole = user.role || 'User';

    // Debug logging
    console.log('🎯 JobList getStatusBadge:', {
      status,
      userRole,
      user: { id: user.id, email: user.email, role: user.role },
      sessionStorageUser: sessionStorage.getItem('user')
    });

    const displayText = getRoleBasedStatusDisplay(status, userRole);
    const colors = getStatusColors(status);

    return (
      <span style={{
        display: 'inline-flex',
        alignItems: 'center',
        padding: '4px 12px',
        borderRadius: '9999px',
        fontSize: '12px',
        fontWeight: '500',
        backgroundColor: colors.bg,
        color: colors.text,
        border: `1px solid ${colors.border}`
      }}>
        {displayText}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        borderRadius: '8px',
        padding: '24px'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '128px'
        }}>
          <div style={{color: '#6b7280'}}>Loading jobs...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        borderRadius: '8px',
        padding: '24px'
      }}>
        <div style={{
          borderRadius: '6px',
          backgroundColor: '#fef2f2',
          padding: '16px'
        }}>
          <div style={{display: 'flex'}}>
            <div style={{flexShrink: 0}}>
              <svg style={{height: '20px', width: '20px', color: '#f87171'}} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div style={{marginLeft: '12px'}}>
              <h3 style={{fontSize: '14px', fontWeight: '500', color: '#991b1b', margin: '0 0 8px 0'}}>Error Loading Jobs</h3>
              <div style={{fontSize: '14px', color: '#7f1d1d', margin: '0'}}>{error}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (jobs.length === 0) {
    return (
      <div style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        borderRadius: '8px'
      }}>
        <div style={{padding: '24px'}}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '500',
            color: '#111827',
            margin: '0 0 24px 0'
          }}>Work History</h3>

          {/* Empty Summary Cards */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px',
            marginBottom: '24px'
          }}>
            <div style={{
              backgroundColor: '#ecfdf5',
              padding: '16px',
              borderRadius: '12px',
              border: '1px solid #d1fae5'
            }}>
              <div style={{display: 'flex', alignItems: 'center'}}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#059669',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>Total Jobs</div>
                  <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>0</div>
                </div>
              </div>
            </div>
            <div style={{
              backgroundColor: '#ecfdf5',
              padding: '16px',
              borderRadius: '12px',
              border: '1px solid #d1fae5'
            }}>
              <div style={{display: 'flex', alignItems: 'center'}}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#059669',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>Completed</div>
                  <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>0</div>
                </div>
              </div>
            </div>
            <div style={{
              backgroundColor: '#ecfdf5',
              padding: '16px',
              borderRadius: '12px',
              border: '1px solid #d1fae5'
            }}>
              <div style={{display: 'flex', alignItems: 'center'}}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#059669',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>In Progress</div>
                  <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>0</div>
                </div>
              </div>
            </div>
            <div style={{
              backgroundColor: '#ecfdf5',
              padding: '16px',
              borderRadius: '12px',
              border: '1px solid #d1fae5'
            }}>
              <div style={{display: 'flex', alignItems: 'center'}}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#059669',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px'
                }}>
                  <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>New</div>
                  <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>0</div>
                </div>
              </div>
            </div>
          </div>

          {/* Empty State Message */}
          <div style={{textAlign: 'center', padding: '32px 0'}}>
            <svg
              style={{
                margin: '0 auto',
                height: '48px',
                width: '48px',
                color: '#9ca3af'
              }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 style={{
              marginTop: '8px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#111827',
              margin: '8px 0'
            }}>No jobs found</h3>
            <p style={{
              marginTop: '4px',
              fontSize: '14px',
              color: '#6b7280',
              margin: '4px 0 24px 0'
            }}>
              {hoursStatistics && hoursStatistics.hoursAvailable <= 0
                ? 'Purchase hours to start submitting jobs.'
                : 'Get started by submitting your first job.'}
            </p>
            {hoursStatistics && hoursStatistics.hoursAvailable > 0 && (
              <div>
                <a
                  href="/submit-job"
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    padding: '8px 16px',
                    border: 'none',
                    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                    fontSize: '14px',
                    fontWeight: '500',
                    borderRadius: '6px',
                    color: 'white',
                    backgroundColor: '#059669',
                    textDecoration: 'none'
                  }}
                >
                  Submit Job
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Calculate summary statistics for all statuses
  const totalJobs = jobs.length;

  // Normalize job status for consistent comparison
  const normalizeStatus = (status: string): string => 
    status ? status.replace(/[-\s]/g, '').toLowerCase() : '';

  // Count jobs by normalized status
  const newJobs = jobs.filter(job => normalizeStatus(job.status) === 'new').length;
  const inProgressJobs = jobs.filter(job => normalizeStatus(job.status) === 'inprogress').length;
  const awaitingReviewJobs = jobs.filter(job => normalizeStatus(job.status) === 'awaitingreview').length;
  const completedJobs = jobs.filter(job => normalizeStatus(job.status) === 'closed').length;

  // Get user role for status display
  const getUserRole = () => {
    try {
      const user = JSON.parse(sessionStorage.getItem('user') || '{}');
      return user.role || 'User';
    } catch (e) {
      console.error('Error parsing user from sessionStorage:', e);
      return 'User';
    }
  };

  const userRole = getUserRole();

  return (
    <div style={{
      backgroundColor: 'white',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      borderRadius: '8px'
    }}>
      <div style={{padding: '24px'}}>
        <h3 style={{
          fontSize: '18px',
          fontWeight: '500',
          color: '#111827',
          margin: '0 0 24px 0'
        }}>Work History</h3>

        {/* Summary Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <div style={{
            backgroundColor: '#ecfdf5',
            padding: '16px',
            borderRadius: '12px',
            border: '1px solid #d1fae5'
          }}>
            <div style={{display: 'flex', alignItems: 'center'}}>
              <div style={{
                width: '40px',
                height: '40px',
                backgroundColor: '#059669',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '12px'
              }}>
                <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>Total Jobs</div>
                <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>{totalJobs}</div>
              </div>
            </div>
          </div>
          <div style={{
            backgroundColor: '#ecfdf5',
            padding: '16px',
            borderRadius: '12px',
            border: '1px solid #d1fae5'
          }}>
            <div style={{display: 'flex', alignItems: 'center'}}>
              <div style={{
                width: '40px',
                height: '40px',
                backgroundColor: '#059669',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '12px'
              }}>
                <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>
                  {getRoleBasedStatusDisplay('Closed', userRole)}
                </div>
                <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>{completedJobs}</div>
              </div>
            </div>
          </div>
          <div style={{
            backgroundColor: '#ecfdf5',
            padding: '16px',
            borderRadius: '12px',
            border: '1px solid #d1fae5'
          }}>
            <div style={{display: 'flex', alignItems: 'center'}}>
              <div style={{
                width: '40px',
                height: '40px',
                backgroundColor: '#059669',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '12px'
              }}>
                <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>
                  {getRoleBasedStatusDisplay('InProgress', userRole)}
                </div>
                <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>{inProgressJobs}</div>
              </div>
            </div>
          </div>
          <div style={{
            backgroundColor: '#ecfdf5',
            padding: '16px',
            borderRadius: '12px',
            border: '1px solid #d1fae5'
          }}>
            <div style={{display: 'flex', alignItems: 'center'}}>
              <div style={{
                width: '40px',
                height: '40px',
                backgroundColor: '#059669',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '12px'
              }}>
                <svg style={{height: '20px', width: '20px', color: 'white'}} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <div style={{fontSize: '14px', fontWeight: '500', color: '#065f46'}}>
                  {getRoleBasedStatusDisplay('New', userRole)}
                </div>
                <div style={{fontSize: '24px', fontWeight: 'bold', color: '#064e3b'}}>{newJobs}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Jobs Table */}
        <div style={{overflow: 'hidden'}}>
          <table style={{minWidth: '100%', borderCollapse: 'collapse'}}>
            <thead style={{backgroundColor: '#f9fafb'}}>
              <tr>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'left',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  Job
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'left',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  Type
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'left',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  Status
                </th>
                <th style={{
                  padding: '12px 24px',
                  textAlign: 'left',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  Created
                </th>
              </tr>
            </thead>
            <tbody style={{backgroundColor: 'white'}}>
              {jobs.map((job, index) => (
                <tr key={job.id} style={{
                  borderTop: index > 0 ? '1px solid #e5e7eb' : 'none'
                }}>
                  <td style={{
                    padding: '16px 24px',
                    whiteSpace: 'nowrap'
                  }}>
                    <div>
                      <div
                        style={{
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#059669',
                          cursor: 'pointer',
                          textDecoration: 'underline'
                        }}
                        onClick={() => router.push(`/jobs/${job.id}`)}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.color = '#047857';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.color = '#059669';
                        }}
                      >
                        {job.title}
                      </div>
                      <div style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '300px'
                      }}>{job.description}</div>
                    </div>
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    whiteSpace: 'nowrap'
                  }}>
                    <div style={{
                      fontSize: '14px',
                      color: '#111827'
                    }}>{formatEnumName(job.jobType)}</div>
                    <div style={{
                      fontSize: '14px',
                      color: '#6b7280'
                    }}>{formatEnumName(job.category)}</div>
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    whiteSpace: 'nowrap'
                  }}>
                    {getStatusBadge(job.status)}
                  </td>
                  <td style={{
                    padding: '16px 24px',
                    whiteSpace: 'nowrap',
                    fontSize: '14px',
                    color: '#6b7280'
                  }}>
                    {formatDate(job.createdAt)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}




