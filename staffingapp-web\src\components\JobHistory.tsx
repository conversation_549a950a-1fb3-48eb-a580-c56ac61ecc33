'use client';

import { useState, useEffect } from 'react';
import { JobHistory, JobHistoryAction } from '@/types/models';
import { getJobHistory } from '@/lib/api/jobHistory';

interface JobHistoryProps {
    jobId: number;
}

export default function JobHistoryComponent({ jobId }: JobHistoryProps) {
    const [history, setHistory] = useState<JobHistory[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchHistory = async () => {
            try {
                setLoading(true);
                const data = await getJobHistory(jobId);
                setHistory(data);
            } catch (err) {
                console.error('Error fetching job history:', err);
                setError(err instanceof Error ? err.message : 'Failed to load job history');
            } finally {
                setLoading(false);
            }
        };

        fetchHistory();
    }, [jobId]);

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getActionIcon = (action: JobHistoryAction) => {
        switch (action) {
            case JobHistoryAction.Created:
                return '📝';
            case JobHistoryAction.Assigned:
                return '👤';
            case JobHistoryAction.Started:
                return '▶️';
            case JobHistoryAction.Completed:
                return '✅';
            case JobHistoryAction.Reviewed:
                return '👁️';
            case JobHistoryAction.Released:
                return '🚀';
            case JobHistoryAction.Satisfied:
                return '😊';
            case JobHistoryAction.Returned:
                return '↩️';
            case JobHistoryAction.TimeLogged:
                return '⏱️';
            case JobHistoryAction.CommentAdded:
                return '💬';
            default:
                return '📋';
        }
    };

    const getActionColor = (action: JobHistoryAction) => {
        switch (action) {
            case JobHistoryAction.Created:
                return 'text-blue-600 bg-blue-50';
            case JobHistoryAction.Assigned:
                return 'text-purple-600 bg-purple-50';
            case JobHistoryAction.Started:
                return 'text-green-600 bg-green-50';
            case JobHistoryAction.Completed:
                return 'text-emerald-600 bg-emerald-50';
            case JobHistoryAction.Reviewed:
                return 'text-indigo-600 bg-indigo-50';
            case JobHistoryAction.Released:
                return 'text-teal-600 bg-teal-50';
            case JobHistoryAction.Satisfied:
                return 'text-green-700 bg-green-100';
            case JobHistoryAction.Returned:
                return 'text-red-600 bg-red-50';
            case JobHistoryAction.TimeLogged:
                return 'text-orange-600 bg-orange-50';
            case JobHistoryAction.CommentAdded:
                return 'text-gray-600 bg-gray-50';
            default:
                return 'text-gray-600 bg-gray-50';
        }
    };

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Job History</h3>
                <div className="animate-pulse space-y-4">
                    {[...Array(3)].map((_, i) => (
                        <div key={i} className="flex space-x-4">
                            <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                            <div className="flex-1 space-y-2">
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Job History</h3>
                <div className="text-red-600 text-sm">{error}</div>
            </div>
        );
    }

    return (
        <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Job History</h3>
            
            {history.length === 0 ? (
                <p className="text-gray-500 text-sm">No history available</p>
            ) : (
                <div className="flow-root">
                    <ul className="-mb-8">
                        {history.map((entry, index) => (
                            <li key={entry.id}>
                                <div className="relative pb-8">
                                    {index !== history.length - 1 && (
                                        <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                                    )}
                                    <div className="relative flex space-x-3">
                                        <div className={`h-8 w-8 rounded-full flex items-center justify-center text-sm ${getActionColor(entry.action)}`}>
                                            {getActionIcon(entry.action)}
                                        </div>
                                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                            <div>
                                                <p className="text-sm text-gray-900">
                                                    <span className="font-medium">{entry.user.firstName} {entry.user.lastName}</span>
                                                    {' '}{entry.details || entry.action}
                                                    {entry.hoursSpent && (
                                                        <span className="text-emerald-600 font-medium"> ({entry.hoursSpent}h)</span>
                                                    )}
                                                </p>
                                                {entry.notes && (
                                                    <p className="mt-1 text-sm text-gray-600">{entry.notes}</p>
                                                )}
                                                {entry.outputDetails && (
                                                    <p className="mt-1 text-sm text-blue-600">Output: {entry.outputDetails}</p>
                                                )}
                                                {entry.assignedTo && (
                                                    <p className="mt-1 text-sm text-purple-600">
                                                        Assigned to: {entry.assignedTo.firstName} {entry.assignedTo.lastName}
                                                    </p>
                                                )}
                                                {entry.previousStatus && entry.newStatus && (
                                                    <p className="mt-1 text-xs text-gray-500">
                                                        Status: {entry.previousStatus} → {entry.newStatus}
                                                    </p>
                                                )}
                                            </div>
                                            <div className="text-right text-sm whitespace-nowrap text-gray-500">
                                                <time dateTime={entry.createdAt}>{formatDate(entry.createdAt)}</time>
                                                <p className="text-xs text-gray-400">{entry.user.role}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
}
