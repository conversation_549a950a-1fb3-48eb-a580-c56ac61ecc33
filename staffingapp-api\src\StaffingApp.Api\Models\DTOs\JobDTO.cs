using System;
using StaffingApp.Api.Models;

namespace StaffingApp.Api.Models.DTOs
{
    public class JobDTO
    {
        public int Id { get; set; }
        public required string Title { get; set; }
        public required string Description { get; set; }
        public required string JobType { get; set; }
        public required string Category { get; set; }
        public required string OutputFormat { get; set; }
        public required string Status { get; set; }
        public string? AttachmentUrl { get; set; }
        public string? ReferenceUrl { get; set; }
        public int ClientId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? AssignedToId { get; set; }
        public User? AssignedTo { get; set; }
        public int? ReviewedById { get; set; }
        public User? ReviewedBy { get; set; }
        public DateTime? AssignedAt { get; set; }
        public DateTime? ReviewedAt { get; set; }
        public string? SupervisorNotes { get; set; }
        public string? OutputDetails { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime? DeliveredAt { get; set; }
        public DateTime? SatisfiedAt { get; set; }
        public List<JobCommentDTO>? Comments { get; set; }
    }

    public class CreateJobDTO
    {
        public required string Title { get; set; }
        public required string Description { get; set; }
        public required string JobType { get; set; }
        public required string Category { get; set; }
        public required string OutputFormat { get; set; }
        public string? AttachmentUrl { get; set; }
        public string? ReferenceUrl { get; set; }
        public int ClientId { get; set; }
    }
}