<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Sample Jobs - Staff Hall</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #059669;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .login-section {
            background-color: #e0f2fe;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Create Sample Jobs with Different Statuses</h1>
        
        <div class="login-section">
            <h3>Step 1: Login as Client</h3>
            <p>Email: <input type="email" id="email" value="<EMAIL>" style="width: 200px;"></p>
            <p>Password: <input type="password" id="password" value="test123" style="width: 200px;"></p>
            <button class="button" onclick="login()">Login</button>
            <p id="loginStatus"></p>
        </div>

        <div>
            <h3>Step 2: Create Sample Jobs</h3>
            <button class="button" onclick="createSampleJobs()">Create Sample Jobs</button>
            <button class="button" onclick="listJobs()">List All Jobs</button>
            <button class="button" onclick="clearResults()">Clear</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let authToken = '';
        let currentUser = null;

        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').textContent = '';
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const statusEl = document.getElementById('loginStatus');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    currentUser = data.user;
                    statusEl.textContent = `✅ Logged in as ${data.user.firstName} ${data.user.lastName} (${data.user.role})`;
                    statusEl.style.color = 'green';
                    log(`Logged in as ${data.user.firstName} ${data.user.lastName} (${data.user.role})`);
                } else {
                    statusEl.textContent = '❌ Login failed';
                    statusEl.style.color = 'red';
                }
            } catch (error) {
                statusEl.textContent = '❌ Login error: ' + error.message;
                statusEl.style.color = 'red';
            }
        }

        async function createSampleJobs() {
            if (!authToken || !currentUser) {
                log('❌ Please login first');
                return;
            }

            log('Creating sample jobs...');

            const sampleJobs = [
                {
                    title: 'Data Entry - Customer Records',
                    description: 'Enter customer information from paper forms into digital database',
                    jobType: 'DataEntry',
                    category: 'DataProcessing',
                    outputFormat: 'Excel'
                },
                {
                    title: 'Document Processing - Legal Files',
                    description: 'Process and organize legal documents for case management',
                    jobType: 'DataEntry',
                    category: 'DocumentationEntry',
                    outputFormat: 'PDF'
                },
                {
                    title: 'Financial Data Analysis',
                    description: 'Analyze quarterly financial reports and create summary',
                    jobType: 'DataEntry',
                    category: 'DataProcessing',
                    outputFormat: 'Excel'
                },
                {
                    title: 'Content Creation - Blog Posts',
                    description: 'Write and format blog posts for company website',
                    jobType: 'DataEntry',
                    category: 'DocumentationEntry',
                    outputFormat: 'Word'
                },
                {
                    title: 'Database Cleanup Project',
                    description: 'Clean and validate customer database entries',
                    jobType: 'DataEntry',
                    category: 'DataCleaning',
                    outputFormat: 'Database'
                }
            ];

            for (let i = 0; i < sampleJobs.length; i++) {
                const job = sampleJobs[i];
                try {
                    const response = await fetch(`${API_BASE}/jobs`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: JSON.stringify({
                            ...job,
                            clientId: currentUser.clientId
                        })
                    });

                    if (response.ok) {
                        const jobData = await response.json();
                        log(`✅ Created job: ${job.title} (ID: ${jobData.id})`);
                    } else {
                        const error = await response.text();
                        log(`❌ Failed to create job: ${job.title} - ${error}`);
                    }
                } catch (error) {
                    log(`❌ Error creating job: ${job.title} - ${error.message}`);
                }
            }

            log('\n🎉 Sample job creation completed!');
            log('Now you can:');
            log('1. Login as different roles (<EMAIL>, <EMAIL>)');
            log('2. Check the Work History section to see role-based status display');
            log('3. Use supervisor to assign jobs to staff');
            log('4. Use staff to start and complete jobs');
        }

        async function listJobs() {
            if (!authToken) {
                log('❌ Please login first');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/jobs`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const jobs = await response.json();
                    log(`📋 Found ${jobs.length} jobs:`);
                    jobs.forEach(job => {
                        log(`  ID: ${job.id}, Title: ${job.title}`);
                        log(`  Status: ${job.status}, Client: ${job.clientId}`);
                        log(`  Created: ${new Date(job.createdAt).toLocaleDateString()}`);
                        log('  ---');
                    });
                } else {
                    log('❌ Failed to fetch jobs');
                }
            } catch (error) {
                log('❌ Error fetching jobs: ' + error.message);
            }
        }

        // Auto-load
        window.onload = function() {
            log('Ready to create sample jobs. Please login first.');
        };
    </script>
</body>
</html>
