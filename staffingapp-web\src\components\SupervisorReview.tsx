'use client';

import { useState } from 'react';
import { ReviewJobRequest } from '@/types/models';
import { reviewJobWithFeedback } from '@/lib/api/jobHistory';

interface SupervisorReviewProps {
    jobId: number;
    jobStatus: string;
    outputDetails?: string;
    onReviewCompleted?: () => void;
}

export default function SupervisorReview({ jobId, jobStatus, outputDetails, onReviewCompleted }: SupervisorReviewProps) {
    const [isReviewing, setIsReviewing] = useState(false);
    const [approved, setApproved] = useState<boolean>(true);
    const [outputFormatAcceptable, setOutputFormatAcceptable] = useState<boolean>(true);
    const [notes, setNotes] = useState<string>('');
    const [outputFormatFeedback, setOutputFormatFeedback] = useState<string>('');
    const [requiredChanges, setRequiredChanges] = useState<string>('');
    const [error, setError] = useState<string | null>(null);

    const canReview = jobStatus === 'Awaiting Review';

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!approved && !requiredChanges.trim()) {
            setError('Please specify required changes when rejecting a job');
            return;
        }

        if (!outputFormatAcceptable && !outputFormatFeedback.trim()) {
            setError('Please provide feedback on output format issues');
            return;
        }

        setIsReviewing(true);
        setError(null);

        try {
            const request: ReviewJobRequest = {
                approved,
                notes: notes.trim() || undefined,
                outputFormatFeedback: outputFormatFeedback.trim() || undefined,
                outputFormatAcceptable,
                requiredChanges: requiredChanges.trim() || undefined
            };

            await reviewJobWithFeedback(jobId, request);
            
            if (onReviewCompleted) {
                onReviewCompleted();
            }
        } catch (err) {
            console.error('Error reviewing job:', err);
            setError(err instanceof Error ? err.message : 'Failed to review job');
        } finally {
            setIsReviewing(false);
        }
    };

    if (!canReview) {
        return (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-center">
                    <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <p className="text-sm text-gray-600">
                            Job review is only available for jobs awaiting review
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <svg className="h-5 w-5 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Review Job
            </h4>

            {outputDetails && (
                <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h5 className="text-sm font-medium text-blue-900 mb-2">Staff Output Details:</h5>
                    <p className="text-sm text-blue-800">{outputDetails}</p>
                </div>
            )}

            {error && (
                <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-red-800">{error}</p>
                        </div>
                    </div>
                </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Approval Decision */}
                <div>
                    <fieldset>
                        <legend className="text-sm font-medium text-gray-900">Review Decision</legend>
                        <div className="mt-2 space-y-2">
                            <div className="flex items-center">
                                <input
                                    id="approve"
                                    name="decision"
                                    type="radio"
                                    checked={approved}
                                    onChange={() => setApproved(true)}
                                    className="focus:ring-emerald-500 h-4 w-4 text-emerald-600 border-gray-300"
                                />
                                <label htmlFor="approve" className="ml-3 block text-sm font-medium text-gray-700">
                                    Approve and Release to Client
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    id="reject"
                                    name="decision"
                                    type="radio"
                                    checked={!approved}
                                    onChange={() => setApproved(false)}
                                    className="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300"
                                />
                                <label htmlFor="reject" className="ml-3 block text-sm font-medium text-gray-700">
                                    Return to Staff for Revisions
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>

                {/* Output Format Check */}
                <div>
                    <fieldset>
                        <legend className="text-sm font-medium text-gray-900">Output Format</legend>
                        <div className="mt-2 space-y-2">
                            <div className="flex items-center">
                                <input
                                    id="format-acceptable"
                                    name="format"
                                    type="radio"
                                    checked={outputFormatAcceptable}
                                    onChange={() => setOutputFormatAcceptable(true)}
                                    className="focus:ring-emerald-500 h-4 w-4 text-emerald-600 border-gray-300"
                                />
                                <label htmlFor="format-acceptable" className="ml-3 block text-sm font-medium text-gray-700">
                                    Output format is acceptable
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    id="format-issues"
                                    name="format"
                                    type="radio"
                                    checked={!outputFormatAcceptable}
                                    onChange={() => setOutputFormatAcceptable(false)}
                                    className="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300"
                                />
                                <label htmlFor="format-issues" className="ml-3 block text-sm font-medium text-gray-700">
                                    Output format needs improvement
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>

                {/* Output Format Feedback */}
                {!outputFormatAcceptable && (
                    <div>
                        <label htmlFor="outputFormatFeedback" className="block text-sm font-medium text-gray-700">
                            Output Format Feedback *
                        </label>
                        <div className="mt-1">
                            <textarea
                                id="outputFormatFeedback"
                                rows={3}
                                value={outputFormatFeedback}
                                onChange={(e) => setOutputFormatFeedback(e.target.value)}
                                className="shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                placeholder="Explain what needs to be improved with the output format..."
                                required={!outputFormatAcceptable}
                            />
                        </div>
                    </div>
                )}

                {/* Required Changes */}
                {!approved && (
                    <div>
                        <label htmlFor="requiredChanges" className="block text-sm font-medium text-gray-700">
                            Required Changes *
                        </label>
                        <div className="mt-1">
                            <textarea
                                id="requiredChanges"
                                rows={3}
                                value={requiredChanges}
                                onChange={(e) => setRequiredChanges(e.target.value)}
                                className="shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                placeholder="Specify what changes are needed..."
                                required={!approved}
                            />
                        </div>
                    </div>
                )}

                {/* General Notes */}
                <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                        Additional Notes (Optional)
                    </label>
                    <div className="mt-1">
                        <textarea
                            id="notes"
                            rows={3}
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            className="shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            placeholder="Any additional feedback or comments..."
                        />
                    </div>
                </div>

                <div className="flex justify-end space-x-3">
                    <button
                        type="submit"
                        disabled={isReviewing}
                        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
                            approved 
                                ? 'bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500'
                                : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                        }`}
                    >
                        {isReviewing ? (
                            <>
                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </>
                        ) : (
                            <>
                                {approved ? (
                                    <>
                                        <svg className="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        Approve & Release
                                    </>
                                ) : (
                                    <>
                                        <svg className="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                        Return for Revisions
                                    </>
                                )}
                            </>
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
}
