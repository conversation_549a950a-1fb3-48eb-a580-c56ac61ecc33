using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StaffingApp.Api.Models.DTOs;
using StaffingApp.Api.Services;
using System.Security.Claims;
using System.Threading.Tasks;

namespace StaffingApp.Api.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class JobsController : ControllerBase
    {
        private readonly StaffingService _staffingService;
        private readonly ILogger<JobsController> _logger;

        public JobsController(StaffingService staffingService, ILogger<JobsController> logger)
        {
            _staffingService = staffingService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<List<JobDTO>>> GetJobs()
        {
            try
            {
                // Get the current user's client ID from the JWT token
                var clientIdClaim = User.FindFirst("ClientId");
                if (clientIdClaim == null || !int.TryParse(clientIdClaim.Value, out var clientId))
                {
                    return Unauthorized("Invalid user token");
                }

                var jobs = await _staffingService.GetJobsAsync(clientId);
                return Ok(jobs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching jobs");
                return StatusCode(500, "An error occurred while fetching jobs");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<JobDTO>> GetJob(int id)
        {
            try
            {
                // Get user information from JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                var clientIdClaim = User.FindFirst("ClientId");
                var roleClaim = User.FindFirst(ClaimTypes.Role);

                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId) ||
                    clientIdClaim == null || !int.TryParse(clientIdClaim.Value, out var clientId) ||
                    roleClaim == null)
                {
                    return Unauthorized("Invalid user token");
                }

                var userRole = roleClaim.Value;
                var job = await _staffingService.GetJobByIdAsync(id, clientId, userId, userRole);
                return Ok(job);
            }
            catch (KeyNotFoundException)
            {
                return NotFound($"Job with ID {id} not found");
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, "You don't have access to this job");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching job {JobId}", id);
                return StatusCode(500, "An error occurred while fetching the job");
            }
        }

        [HttpPost]
        public async Task<ActionResult<JobDTO>> CreateJob(CreateJobDTO createJob)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                var jobDto = await _staffingService.CreateJobAsync(createJob, userId);
                return Ok(jobDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating job");
                return StatusCode(500, "An error occurred while creating the job");
            }
        }

        [HttpGet("all")]
        [Authorize(Roles = "Admin,Supervisor")]
        public async Task<ActionResult<IEnumerable<JobDTO>>> GetAllJobs()
        {
            try
            {
                var jobs = await _staffingService.GetAllJobsAsync();
                return Ok(jobs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all jobs");
                return StatusCode(500, "An error occurred while getting all jobs");
            }
        }

        [HttpGet("assigned")]
        [Authorize(Roles = "Staff")]
        public async Task<ActionResult<IEnumerable<JobDTO>>> GetAssignedJobs()
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                var jobs = await _staffingService.GetAssignedJobsAsync(userId);
                return Ok(jobs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assigned jobs");
                return StatusCode(500, "An error occurred while getting assigned jobs");
            }
        }

        [HttpPut("{id}/assign")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult> AssignJob(int id, [FromBody] AssignJobRequest request)
        {
            try
            {
                // Get the current user ID from the JWT token (supervisor)
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var supervisorId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.AssignJobAsync(id, request.StaffId, supervisorId, request.Notes);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning job");
                return StatusCode(500, "An error occurred while assigning the job");
            }
        }

        [HttpPut("{id}/start")]
        [Authorize(Roles = "Staff")]
        public async Task<ActionResult> StartJob(int id)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.StartJobAsync(id, userId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting job");
                return StatusCode(500, "An error occurred while starting the job");
            }
        }

        [HttpPut("{id}/complete")]
        [Authorize(Roles = "Staff")]
        public async Task<ActionResult> CompleteJob(int id, [FromBody] CompleteJobRequest request)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.CompleteJobAsync(id, userId, request.OutputDetails);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing job");
                return StatusCode(500, "An error occurred while completing the job");
            }
        }

        [HttpPut("{id}/reject")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult> RejectJob(int id)
        {
            try
            {
                // Get the current user ID from the JWT token (supervisor)
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var supervisorId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.RejectJobAsync(id, supervisorId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting job");
                return StatusCode(500, "An error occurred while rejecting the job");
            }
        }

        [HttpPut("{id}/review-completed")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult> ReviewCompletedJob(int id)
        {
            try
            {
                // Get the current user ID from the JWT token (supervisor)
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var supervisorId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.ReviewCompletedJobAsync(id, supervisorId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reviewing completed job");
                return StatusCode(500, "An error occurred while reviewing the completed job");
            }
        }

        [HttpPut("{id}/review-with-feedback")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult> ReviewJobWithFeedback(int id, [FromBody] ReviewJobRequest request)
        {
            try
            {
                // Get the current user ID from the JWT token (supervisor)
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var supervisorId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.ReviewJobWithFeedbackAsync(id, supervisorId, request);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reviewing job with feedback");
                return StatusCode(500, "An error occurred while reviewing the job");
            }
        }

        [HttpGet("{id}/history")]
        [Authorize]
        public async Task<ActionResult<List<JobHistoryDTO>>> GetJobHistory(int id)
        {
            try
            {
                var history = await _staffingService.GetJobHistoryAsync(id);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting job history");
                return StatusCode(500, "An error occurred while getting job history");
            }
        }

        [HttpPost("{id}/log-time")]
        [Authorize(Roles = "Staff")]
        public async Task<ActionResult> LogTime(int id, [FromBody] LogTimeRequest request)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var staffId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.LogTimeAsync(id, staffId, request);
                return Ok();
            }
            catch (KeyNotFoundException)
            {
                return NotFound($"Job with ID {id} not found");
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging time for job");
                return StatusCode(500, "An error occurred while logging time");
            }
        }

        [HttpPut("{id}/tasks/{taskId}/approve")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult> ApproveTimeEntry(int id, int taskId, [FromBody] TimeEntryApprovalRequest request)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var supervisorId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.ApproveTimeEntryAsync(id, taskId, supervisorId, request.Approved, request.Notes);
                return Ok();
            }
            catch (KeyNotFoundException)
            {
                return NotFound($"Task with ID {taskId} not found for job {id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving time entry");
                return StatusCode(500, "An error occurred while approving time entry");
            }
        }

        [HttpPut("{id}/mark-satisfied")]
        [Authorize(Roles = "User")]
        public async Task<ActionResult> MarkJobSatisfied(int id)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.MarkJobSatisfiedAsync(id, userId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking job as satisfied");
                return StatusCode(500, "An error occurred while marking the job as satisfied");
            }
        }

        [HttpPost("{id}/comments")]
        [Authorize]
        public async Task<ActionResult<JobCommentDTO>> AddJobComment(int id, [FromBody] AddJobCommentRequest request)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                var comment = await _staffingService.AddJobCommentAsync(id, userId, request.Content);
                return Ok(comment);
            }
            catch (KeyNotFoundException)
            {
                return NotFound($"Job with ID {id} not found");
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding comment to job {JobId}", id);
                return StatusCode(500, "An error occurred while adding the comment");
            }
        }

        [HttpGet("debug-access/{id}")]
        [Authorize]
        public async Task<ActionResult> DebugAccess(int id)
        {
            try
            {
                // Get user information from JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                var clientIdClaim = User.FindFirst("ClientId");
                var roleClaim = User.FindFirst(ClaimTypes.Role);

                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId) ||
                    clientIdClaim == null || !int.TryParse(clientIdClaim.Value, out var clientId) ||
                    roleClaim == null)
                {
                    return Ok(new { error = "Invalid user token", userIdClaim, clientIdClaim, roleClaim });
                }

                var debugInfo = await _staffingService.GetDebugInfoAsync(id, clientId, userId, roleClaim.Value);

                return Ok(debugInfo);
            }
            catch (Exception ex)
            {
                return Ok(new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }


    }

    public class AssignJobRequest
    {
        public int StaffId { get; set; }
        public string? Notes { get; set; }
    }

    public class CompleteJobRequest
    {
        public required string OutputDetails { get; set; }
    }

    public class AddJobCommentRequest
    {
        public required string Content { get; set; }
    }
}