{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-api\\src\\staffingapp.api\\StaffingApp.Api.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-api\\src\\staffingapp.api\\StaffingApp.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-api\\src\\staffingapp.api\\StaffingApp.Api.csproj", "projectName": "StaffingApp.Api", "projectPath": "C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-api\\src\\staffingapp.api\\StaffingApp.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-api\\src\\staffingapp.api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}