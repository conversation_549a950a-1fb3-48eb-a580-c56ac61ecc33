/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/jobs/[id]/page";
exports.ids = ["app/jobs/[id]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?4c03":
/*!***********************!*\
  !*** debug (ignored) ***!
  \***********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'jobs',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/jobs/[id]/page.tsx */ \"(rsc)/./src/app/jobs/[id]/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/jobs/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/jobs/[id]/page\",\n        pathname: \"/jobs/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cjobs%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cjobs%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/jobs/[id]/page.tsx */ \"(ssr)/./src/app/jobs/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWUlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q3N0YWZmaW5nYXBwJTVDJTVDc3RhZmZpbmdhcHAtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDam9icyU1QyU1QyU1QmlkJTVEJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUF1SSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0YWZmaW5nYXBwLXdlYi8/ZTQ2MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhvbWVcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXHN0YWZmaW5nYXBwXFxcXHN0YWZmaW5nYXBwLXdlYlxcXFxzcmNcXFxcYXBwXFxcXGpvYnNcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cjobs%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/jobs/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/jobs/[id]/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_job__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/job */ \"(ssr)/./src/lib/job.ts\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(ssr)/./src/utils/statusDisplay.ts\");\n/* harmony import */ var _components_JobHistory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/JobHistory */ \"(ssr)/./src/components/JobHistory.tsx\");\n/* harmony import */ var _components_TimeTracking__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TimeTracking */ \"(ssr)/./src/components/TimeTracking.tsx\");\n/* harmony import */ var _components_SupervisorReview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/SupervisorReview */ \"(ssr)/./src/components/SupervisorReview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction JobDetailsPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [job, setJob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Workflow state\n    const [showCompleteModal, setShowCompleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [outputDetails, setOutputDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const jobId = params.id ? parseInt(params.id) : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!jobId) {\n            setError(\"Invalid job ID\");\n            setIsLoading(false);\n            return;\n        }\n        // Get user info\n        const userStr = sessionStorage.getItem(\"user\");\n        if (userStr) {\n            setUser(JSON.parse(userStr));\n        }\n        const fetchJob = async ()=>{\n            try {\n                console.log(\"JobDetailsPage: Fetching job with ID:\", jobId);\n                const jobData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(jobId);\n                console.log(\"JobDetailsPage: Job fetched successfully:\", jobData);\n                setJob(jobData);\n            } catch (err) {\n                console.error(\"JobDetailsPage: Error fetching job:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Failed to fetch job details\";\n                setError(errorMessage);\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"401\")) {\n                    router.push(\"/login\");\n                }\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchJob();\n    }, [\n        jobId,\n        router\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    // Workflow action handlers\n    const handleCompleteJob = async ()=>{\n        if (!job || !outputDetails.trim()) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.completeJob)(job.id, outputDetails.trim());\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n            setShowCompleteModal(false);\n            setOutputDetails(\"\");\n        } catch (err) {\n            console.error(\"Error completing job:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to complete job\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleReviewJob = async ()=>{\n        if (!job) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.reviewCompletedJob)(job.id);\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n        } catch (err) {\n            console.error(\"Error reviewing job:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to review job\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleMarkSatisfied = async ()=>{\n        if (!job) return;\n        setIsSubmitting(true);\n        try {\n            await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.markJobSatisfied)(job.id);\n            // Refresh job data\n            const updatedJob = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n            setJob(updatedJob);\n        } catch (err) {\n            console.error(\"Error marking job as satisfied:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to mark job as satisfied\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Determine available actions based on user role and job status\n    const getAvailableActions = ()=>{\n        if (!job || !user) return [];\n        const actions = [];\n        // Staff actions\n        if (user.role === \"Staff\" && job.status === \"InProgress\") {\n            actions.push({\n                type: \"complete\",\n                label: \"Complete Job\",\n                color: \"emerald\",\n                action: ()=>setShowCompleteModal(true)\n            });\n        }\n        // Supervisor actions\n        if (user.role === \"Supervisor\" && job.status === \"AwaitingReview\") {\n            actions.push({\n                type: \"review\",\n                label: \"Review & Deliver\",\n                color: \"blue\",\n                action: handleReviewJob\n            });\n        }\n        // Client actions\n        if (user.role === \"User\" && job.status === \"Released\") {\n            actions.push({\n                type: \"satisfy\",\n                label: \"Mark as Satisfied\",\n                color: \"green\",\n                action: handleMarkSatisfied\n            });\n        }\n        return actions;\n    };\n    const getStatusBadge = (status)=>{\n        const displayText = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getRoleBasedStatusDisplay)(status, user?.role || \"User\");\n        const colors = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getStatusColors)(status);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            style: {\n                backgroundColor: colors.bg,\n                color: colors.text,\n                border: `1px solid ${colors.border}`,\n                padding: \"4px 12px\",\n                borderRadius: \"9999px\",\n                fontSize: \"12px\",\n                fontWeight: \"500\"\n            },\n            children: displayText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-4 text-gray-600\",\n                                        children: \"Loading job details...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-red-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-red-800\",\n                                                children: \"Error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-sm text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/dashboard\"),\n                                                    className: \"bg-emerald-100 hover:bg-emerald-200 text-emerald-800 px-4 py-2 rounded-md text-sm font-medium\",\n                                                    children: \"Back to Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this);\n    }\n    if (!job) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-emerald-600\",\n                                        children: \"Staff Hall\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"pt-24 py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Job not found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/dashboard\"),\n                                    className: \"mt-4 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Back to Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n            lineNumber: 263,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-emerald-600\",\n                                            children: \"Staff Hall\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/dashboard\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/submit-job\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Submit Job\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/buy-hours\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Buy Hours\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500\",\n                                    onClick: ()=>{\n                                        sessionStorage.clear();\n                                        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n                                        window.location.href = \"/login\";\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/dashboard\"),\n                                    className: \"mb-4 text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: job.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this),\n                                        getStatusBadge(job.status)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow overflow-hidden sm:rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-5 sm:px-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Job Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 max-w-2xl text-sm text-gray-500\",\n                                            children: \"Complete details about this job.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Job ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: [\n                                                            \"#\",\n                                                            job.id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Job Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.jobType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Output Format\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.outputFormat\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: getStatusBadge(job.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            job.updatedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.updatedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.attachmentUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Attachment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: job.attachmentUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-emerald-600 hover:text-emerald-800 underline\",\n                                                            children: \"View Attachment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.referenceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Reference URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: job.referenceUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-emerald-600 hover:text-emerald-800 underline\",\n                                                            children: \"View Reference\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.outputDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Output Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: job.outputDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.completedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Completed At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.completedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.deliveredAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Delivered At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.deliveredAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this),\n                                            job.satisfiedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                        className: \"text-sm font-medium text-gray-500\",\n                                                        children: \"Satisfied At\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                        className: \"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\",\n                                                        children: formatDate(job.satisfiedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        getAvailableActions().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 bg-white shadow sm:rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900 mb-4\",\n                                        children: \"Available Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: getAvailableActions().map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: action.action,\n                                                disabled: isSubmitting,\n                                                className: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${action.color === \"emerald\" ? \"bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500\" : action.color === \"blue\" ? \"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500\" : action.color === \"green\" ? \"bg-green-600 hover:bg-green-700 focus:ring-green-500\" : \"bg-gray-600 hover:bg-gray-700 focus:ring-gray-500\"}`,\n                                                children: isSubmitting ? \"Processing...\" : action.label\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, this),\n                        user?.role === \"Staff\" && job?.assignedToId === user?.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TimeTracking__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                jobId: job.id,\n                                jobStatus: job.status,\n                                onTimeLogged: ()=>{\n                                    // Refresh job data to update history\n                                    const fetchJob = async ()=>{\n                                        try {\n                                            const jobData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n                                            setJob(jobData);\n                                        } catch (err) {\n                                            console.error(\"Error refreshing job:\", err);\n                                        }\n                                    };\n                                    fetchJob();\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this),\n                        user?.role === \"Supervisor\" && job?.status === \"Awaiting Review\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SupervisorReview__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                jobId: job.id,\n                                jobStatus: job.status,\n                                outputDetails: job.outputDetails,\n                                onReviewCompleted: ()=>{\n                                    // Refresh job data\n                                    const fetchJob = async ()=>{\n                                        try {\n                                            const jobData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobById)(job.id);\n                                            setJob(jobData);\n                                        } catch (err) {\n                                            console.error(\"Error refreshing job:\", err);\n                                        }\n                                    };\n                                    fetchJob();\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobHistory__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                jobId: job.id\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this),\n                        showCompleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: [\n                                                \"Complete Job: \",\n                                                job?.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"outputDetails\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Output Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"outputDetails\",\n                                                    rows: 4,\n                                                    value: outputDetails,\n                                                    onChange: (e)=>setOutputDetails(e.target.value),\n                                                    placeholder: \"Describe how the client can access the output (e.g., 'Document has been emailed to client', 'Files uploaded to shared folder', etc.)\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setShowCompleteModal(false);\n                                                        setOutputDetails(\"\");\n                                                    },\n                                                    className: \"flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500\",\n                                                    disabled: isSubmitting,\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleCompleteJob,\n                                                    disabled: isSubmitting || !outputDetails.trim(),\n                                                    className: \"flex-1 px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isSubmitting ? \"Completing...\" : \"Complete Job\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\jobs\\\\[id]\\\\page.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/jobs/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/JobHistory.tsx":
/*!***************************************!*\
  !*** ./src/components/JobHistory.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobHistoryComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/models */ \"(ssr)/./src/types/models.ts\");\n/* harmony import */ var _lib_api_jobHistory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/jobHistory */ \"(ssr)/./src/lib/api/jobHistory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction JobHistoryComponent({ jobId }) {\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchHistory = async ()=>{\n            try {\n                setLoading(true);\n                const data = await (0,_lib_api_jobHistory__WEBPACK_IMPORTED_MODULE_3__.getJobHistory)(jobId);\n                setHistory(data);\n            } catch (err) {\n                console.error(\"Error fetching job history:\", err);\n                setError(err instanceof Error ? err.message : \"Failed to load job history\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchHistory();\n    }, [\n        jobId\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const getActionIcon = (action)=>{\n        switch(action){\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Created:\n                return \"\\uD83D\\uDCDD\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Assigned:\n                return \"\\uD83D\\uDC64\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Started:\n                return \"▶️\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Completed:\n                return \"✅\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Reviewed:\n                return \"\\uD83D\\uDC41️\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Released:\n                return \"\\uD83D\\uDE80\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Satisfied:\n                return \"\\uD83D\\uDE0A\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Returned:\n                return \"↩️\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.TimeLogged:\n                return \"⏱️\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.CommentAdded:\n                return \"\\uD83D\\uDCAC\";\n            default:\n                return \"\\uD83D\\uDCCB\";\n        }\n    };\n    const getActionColor = (action)=>{\n        switch(action){\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Created:\n                return \"text-blue-600 bg-blue-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Assigned:\n                return \"text-purple-600 bg-purple-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Started:\n                return \"text-green-600 bg-green-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Completed:\n                return \"text-emerald-600 bg-emerald-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Reviewed:\n                return \"text-indigo-600 bg-indigo-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Released:\n                return \"text-teal-600 bg-teal-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Satisfied:\n                return \"text-green-700 bg-green-100\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.Returned:\n                return \"text-red-600 bg-red-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.TimeLogged:\n                return \"text-orange-600 bg-orange-50\";\n            case _types_models__WEBPACK_IMPORTED_MODULE_2__.JobHistoryAction.CommentAdded:\n                return \"text-gray-600 bg-gray-50\";\n            default:\n                return \"text-gray-600 bg-gray-50\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                    children: \"Job History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-4\",\n                    children: [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 25\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n            lineNumber: 99,\n            columnNumber: 13\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow rounded-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                    children: \"Job History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n            lineNumber: 118,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow rounded-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-6\",\n                children: \"Job History\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                lineNumber: 127,\n                columnNumber: 13\n            }, this),\n            history.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-500 text-sm\",\n                children: \"No history available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                lineNumber: 130,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flow-root\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"-mb-8\",\n                    children: history.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative pb-8\",\n                                children: [\n                                    index !== history.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 41\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `h-8 w-8 rounded-full flex items-center justify-center text-sm ${getActionColor(entry.action)}`,\n                                                children: getActionIcon(entry.action)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            entry.user.firstName,\n                                                                            \" \",\n                                                                            entry.user.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                        lineNumber: 147,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    entry.details || entry.action,\n                                                                    entry.hoursSpent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-emerald-600 font-medium\",\n                                                                        children: [\n                                                                            \" (\",\n                                                                            entry.hoursSpent,\n                                                                            \"h)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                        lineNumber: 150,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            entry.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-sm text-gray-600\",\n                                                                children: entry.notes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            entry.outputDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-sm text-blue-600\",\n                                                                children: [\n                                                                    \"Output: \",\n                                                                    entry.outputDetails\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            entry.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-sm text-purple-600\",\n                                                                children: [\n                                                                    \"Assigned to: \",\n                                                                    entry.assignedTo.firstName,\n                                                                    \" \",\n                                                                    entry.assignedTo.lastName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            entry.previousStatus && entry.newStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    entry.previousStatus,\n                                                                    \" → \",\n                                                                    entry.newStatus\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right text-sm whitespace-nowrap text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                                dateTime: entry.createdAt,\n                                                                children: formatDate(entry.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: entry.user.role\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 33\n                            }, this)\n                        }, entry.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 29\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n                lineNumber: 132,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobHistory.tsx\",\n        lineNumber: 126,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/JobHistory.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SupervisorReview.tsx":
/*!*********************************************!*\
  !*** ./src/components/SupervisorReview.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupervisorReview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_jobHistory__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/jobHistory */ \"(ssr)/./src/lib/api/jobHistory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SupervisorReview({ jobId, jobStatus, outputDetails, onReviewCompleted }) {\n    const [isReviewing, setIsReviewing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [approved, setApproved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [outputFormatAcceptable, setOutputFormatAcceptable] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [outputFormatFeedback, setOutputFormatFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [requiredChanges, setRequiredChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canReview = jobStatus === \"Awaiting Review\";\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!approved && !requiredChanges.trim()) {\n            setError(\"Please specify required changes when rejecting a job\");\n            return;\n        }\n        if (!outputFormatAcceptable && !outputFormatFeedback.trim()) {\n            setError(\"Please provide feedback on output format issues\");\n            return;\n        }\n        setIsReviewing(true);\n        setError(null);\n        try {\n            const request = {\n                approved,\n                notes: notes.trim() || undefined,\n                outputFormatFeedback: outputFormatFeedback.trim() || undefined,\n                outputFormatAcceptable,\n                requiredChanges: requiredChanges.trim() || undefined\n            };\n            await (0,_lib_api_jobHistory__WEBPACK_IMPORTED_MODULE_2__.reviewJobWithFeedback)(jobId, request);\n            if (onReviewCompleted) {\n                onReviewCompleted();\n            }\n        } catch (err) {\n            console.error(\"Error reviewing job:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to review job\");\n        } finally{\n            setIsReviewing(false);\n        }\n    };\n    if (!canReview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5 text-gray-400\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"Job review is only available for jobs awaiting review\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                lineNumber: 66,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n            lineNumber: 65,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-5 w-5 text-indigo-500 mr-2\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 17\n                    }, this),\n                    \"Review Job\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, this),\n            outputDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                        children: \"Staff Output Details:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-800\",\n                        children: outputDetails\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                lineNumber: 92,\n                columnNumber: 17\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-red-50 border border-red-200 rounded-md p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                lineNumber: 99,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"fieldset\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"legend\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: \"Review Decision\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"approve\",\n                                                    name: \"decision\",\n                                                    type: \"radio\",\n                                                    checked: approved,\n                                                    onChange: ()=>setApproved(true),\n                                                    className: \"focus:ring-emerald-500 h-4 w-4 text-emerald-600 border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"approve\",\n                                                    className: \"ml-3 block text-sm font-medium text-gray-700\",\n                                                    children: \"Approve and Release to Client\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"reject\",\n                                                    name: \"decision\",\n                                                    type: \"radio\",\n                                                    checked: !approved,\n                                                    onChange: ()=>setApproved(false),\n                                                    className: \"focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"reject\",\n                                                    className: \"ml-3 block text-sm font-medium text-gray-700\",\n                                                    children: \"Return to Staff for Revisions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"fieldset\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"legend\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: \"Output Format\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"format-acceptable\",\n                                                    name: \"format\",\n                                                    type: \"radio\",\n                                                    checked: outputFormatAcceptable,\n                                                    onChange: ()=>setOutputFormatAcceptable(true),\n                                                    className: \"focus:ring-emerald-500 h-4 w-4 text-emerald-600 border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"format-acceptable\",\n                                                    className: \"ml-3 block text-sm font-medium text-gray-700\",\n                                                    children: \"Output format is acceptable\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"format-issues\",\n                                                    name: \"format\",\n                                                    type: \"radio\",\n                                                    checked: !outputFormatAcceptable,\n                                                    onChange: ()=>setOutputFormatAcceptable(false),\n                                                    className: \"focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"format-issues\",\n                                                    className: \"ml-3 block text-sm font-medium text-gray-700\",\n                                                    children: \"Output format needs improvement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 17\n                    }, this),\n                    !outputFormatAcceptable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"outputFormatFeedback\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Output Format Feedback *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"outputFormatFeedback\",\n                                    rows: 3,\n                                    value: outputFormatFeedback,\n                                    onChange: (e)=>setOutputFormatFeedback(e.target.value),\n                                    className: \"shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md\",\n                                    placeholder: \"Explain what needs to be improved with the output format...\",\n                                    required: !outputFormatAcceptable\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 21\n                    }, this),\n                    !approved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"requiredChanges\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Required Changes *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"requiredChanges\",\n                                    rows: 3,\n                                    value: requiredChanges,\n                                    onChange: (e)=>setRequiredChanges(e.target.value),\n                                    className: \"shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md\",\n                                    placeholder: \"Specify what changes are needed...\",\n                                    required: !approved\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"notes\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Additional Notes (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"notes\",\n                                    rows: 3,\n                                    value: notes,\n                                    onChange: (e)=>setNotes(e.target.value),\n                                    className: \"shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md\",\n                                    placeholder: \"Any additional feedback or comments...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isReviewing,\n                            className: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${approved ? \"bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500\" : \"bg-red-600 hover:bg-red-700 focus:ring-red-500\"}`,\n                            children: isReviewing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 33\n                                    }, this),\n                                    \"Processing...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: approved ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"-ml-1 mr-2 h-5 w-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 41\n                                        }, this),\n                                        \"Approve & Release\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"-ml-1 mr-2 h-5 w-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 41\n                                        }, this),\n                                        \"Return for Revisions\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n                lineNumber: 113,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\SupervisorReview.tsx\",\n        lineNumber: 83,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SupervisorReview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TimeTracking.tsx":
/*!*****************************************!*\
  !*** ./src/components/TimeTracking.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimeTracking)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_jobHistory__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/jobHistory */ \"(ssr)/./src/lib/api/jobHistory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TimeTracking({ jobId, jobStatus, onTimeLogged }) {\n    const [isLogging, setIsLogging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoursSpent, setHoursSpent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canLogTime = jobStatus === \"In Progress\";\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!hoursSpent || parseFloat(hoursSpent) <= 0) {\n            setError(\"Please enter valid hours\");\n            return;\n        }\n        setIsLogging(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const request = {\n                hoursSpent: parseFloat(hoursSpent),\n                description: description.trim() || undefined\n            };\n            await (0,_lib_api_jobHistory__WEBPACK_IMPORTED_MODULE_2__.logTime)(jobId, request);\n            setSuccess(`Successfully logged ${hoursSpent} hours`);\n            setHoursSpent(\"\");\n            setDescription(\"\");\n            if (onTimeLogged) {\n                onTimeLogged();\n            }\n        } catch (err) {\n            console.error(\"Error logging time:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to log time\");\n        } finally{\n            setIsLogging(false);\n        }\n    };\n    if (!canLogTime) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5 text-gray-400\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.414-1.414L11 9.586V6z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600\",\n                            children: \"Time tracking is only available for jobs in progress\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                lineNumber: 60,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n            lineNumber: 59,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-5 w-5 text-emerald-500 mr-2\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.414-1.414L11 9.586V6z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 17\n                    }, this),\n                    \"Log Time\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                lineNumber: 78,\n                columnNumber: 13\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-red-50 border border-red-200 rounded-md p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-red-400\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                lineNumber: 86,\n                columnNumber: 17\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 bg-green-50 border border-green-200 rounded-md p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-green-400\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-green-800\",\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                lineNumber: 101,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"hoursSpent\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Hours Spent *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    id: \"hoursSpent\",\n                                    value: hoursSpent,\n                                    onChange: (e)=>setHoursSpent(e.target.value),\n                                    min: \"0.1\",\n                                    step: \"0.1\",\n                                    className: \"shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md\",\n                                    placeholder: \"e.g., 2.5\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"description\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Description (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    rows: 3,\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    className: \"shadow-sm focus:ring-emerald-500 focus:border-emerald-500 block w-full sm:text-sm border-gray-300 rounded-md\",\n                                    placeholder: \"Describe what you worked on...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLogging,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isLogging ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 33\n                                    }, this),\n                                    \"Logging...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"-ml-1 mr-2 h-5 w-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.414-1.414L11 9.586V6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 33\n                                    }, this),\n                                    \"Log Time\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n                lineNumber: 115,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\TimeTracking.tsx\",\n        lineNumber: 77,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TimeTracking.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/jobHistory.ts":
/*!***********************************!*\
  !*** ./src/lib/api/jobHistory.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   approveTimeEntry: () => (/* binding */ approveTimeEntry),\n/* harmony export */   getJobHistory: () => (/* binding */ getJobHistory),\n/* harmony export */   logTime: () => (/* binding */ logTime),\n/* harmony export */   reviewJobWithFeedback: () => (/* binding */ reviewJobWithFeedback)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:5000\" || 0;\n// Get job history\nconst getJobHistory = async (jobId)=>{\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/jobs/${jobId}/history`, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    return response.data;\n};\n// Log time for a job (staff only)\nconst logTime = async (jobId, request)=>{\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/api/jobs/${jobId}/log-time`, request, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n// Review job with feedback (supervisor only)\nconst reviewJobWithFeedback = async (jobId, request)=>{\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/review-with-feedback`, request, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n// Approve or reject time entry (supervisor only)\nconst approveTimeEntry = async (jobId, taskId, request)=>{\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/tasks/${taskId}/approve`, request, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/jobHistory.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/job.ts":
/*!************************!*\
  !*** ./src/lib/job.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addJobComment: () => (/* binding */ addJobComment),\n/* harmony export */   completeJob: () => (/* binding */ completeJob),\n/* harmony export */   createJob: () => (/* binding */ createJob),\n/* harmony export */   getJobById: () => (/* binding */ getJobById),\n/* harmony export */   getJobs: () => (/* binding */ getJobs),\n/* harmony export */   markJobSatisfied: () => (/* binding */ markJobSatisfied),\n/* harmony export */   reviewCompletedJob: () => (/* binding */ reviewCompletedJob)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:5000\" || 0;\nconst getJobs = async ()=>{\n    console.log(\"getJobs: Starting...\");\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        console.log(\"getJobs: Not in browser, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    // Add a small delay to ensure sessionStorage is ready\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    const token = sessionStorage.getItem(\"token\");\n    const user = sessionStorage.getItem(\"user\");\n    console.log(\"getJobs: Token exists:\", !!token);\n    console.log(\"getJobs: User exists:\", !!user);\n    console.log(\"getJobs: SessionStorage keys:\", Object.keys(sessionStorage));\n    if (token) {\n        console.log(\"getJobs: Token preview:\", token.substring(0, 20) + \"...\");\n        console.log(\"getJobs: Token length:\", token.length);\n    } else {\n        console.log(\"getJobs: Token is null/undefined/empty\");\n        console.log(\"getJobs: All sessionStorage items:\", {\n            token: sessionStorage.getItem(\"token\"),\n            user: sessionStorage.getItem(\"user\")\n        });\n    }\n    if (!token) {\n        console.log(\"getJobs: No token found, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    console.log(\"getJobs: Making API request to:\", `${API_URL}/api/jobs`);\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/jobs`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        console.log(\"getJobs: Response status:\", response.status);\n        console.log(\"getJobs: Response data type:\", typeof response.data);\n        console.log(\"getJobs: Response data length:\", Array.isArray(response.data) ? response.data.length : \"Not an array\");\n        console.log(\"getJobs: Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"getJobs: API request failed:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"getJobs: Axios error details:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data\n            });\n            if (error.response?.status === 401) {\n                throw new Error(\"Authentication required\");\n            }\n        }\n        throw error;\n    }\n};\nconst getJobById = async (id)=>{\n    console.log(\"getJobById: Starting for job ID:\", id);\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        console.log(\"getJobById: Not in browser, throwing auth error\");\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        console.log(\"getJobById: No token found\");\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        console.log(\"getJobById: Making API request to:\", `${API_URL}/api/jobs/${id}`);\n        console.log(\"getJobById: Using token:\", token.substring(0, 20) + \"...\");\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/jobs/${id}`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        console.log(\"getJobById: Response status:\", response.status);\n        console.log(\"getJobById: Response data:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"getJobById: API request failed:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"getJobById: Axios error details:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data\n            });\n            if (error.response?.status === 401) {\n                throw new Error(\"Authentication required\");\n            }\n            if (error.response?.status === 404) {\n                throw new Error(\"Job not found\");\n            }\n            if (error.response?.status === 403) {\n                throw new Error(\"Access denied\");\n            }\n        }\n        throw error;\n    }\n};\nconst createJob = async (jobData)=>{\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/api/jobs`, jobData, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    return response.data;\n};\n// New workflow API functions\nconst completeJob = async (jobId, outputDetails)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/complete`, {\n        outputDetails\n    }, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\nconst reviewCompletedJob = async (jobId)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/review-completed`, {}, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\nconst markJobSatisfied = async (jobId)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`${API_URL}/api/jobs/${jobId}/mark-satisfied`, {}, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\nconst addJobComment = async (jobId, content)=>{\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/api/jobs/${jobId}/comments`, {\n        content\n    }, {\n        headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/job.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/models.ts":
/*!*****************************!*\
  !*** ./src/types/models.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JobCategory: () => (/* binding */ JobCategory),\n/* harmony export */   JobHistoryAction: () => (/* binding */ JobHistoryAction),\n/* harmony export */   JobStatus: () => (/* binding */ JobStatus),\n/* harmony export */   JobType: () => (/* binding */ JobType),\n/* harmony export */   OutputFormat: () => (/* binding */ OutputFormat),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\nvar JobType;\n(function(JobType) {\n    JobType[\"DataEntry\"] = \"DataEntry\";\n    JobType[\"Accounting\"] = \"Accounting\";\n    JobType[\"HR\"] = \"HR\";\n    JobType[\"ITSupport\"] = \"ITSupport\";\n    JobType[\"Marketing\"] = \"Marketing\";\n    JobType[\"Legal\"] = \"Legal\";\n    JobType[\"CustomerService\"] = \"CustomerService\";\n    JobType[\"Other\"] = \"Other\";\n})(JobType || (JobType = {}));\nvar JobCategory;\n(function(JobCategory) {\n    // Data Entry Categories\n    JobCategory[\"DataProcessing\"] = \"DataProcessing\";\n    JobCategory[\"DataCleaning\"] = \"DataCleaning\";\n    JobCategory[\"DocumentationEntry\"] = \"DocumentationEntry\";\n    // Accounting Categories\n    JobCategory[\"Bookkeeping\"] = \"Bookkeeping\";\n    JobCategory[\"FinancialReporting\"] = \"FinancialReporting\";\n    JobCategory[\"Taxation\"] = \"Taxation\";\n    JobCategory[\"Payroll\"] = \"Payroll\";\n    // HR Categories\n    JobCategory[\"Recruitment\"] = \"Recruitment\";\n    JobCategory[\"EmployeeRelations\"] = \"EmployeeRelations\";\n    JobCategory[\"Training\"] = \"Training\";\n    JobCategory[\"CompensationBenefits\"] = \"CompensationBenefits\";\n    // IT Support Categories\n    JobCategory[\"TechnicalSupport\"] = \"TechnicalSupport\";\n    JobCategory[\"NetworkSupport\"] = \"NetworkSupport\";\n    JobCategory[\"SoftwareSupport\"] = \"SoftwareSupport\";\n    JobCategory[\"HardwareSupport\"] = \"HardwareSupport\";\n    // Marketing Categories\n    JobCategory[\"DigitalMarketing\"] = \"DigitalMarketing\";\n    JobCategory[\"ContentCreation\"] = \"ContentCreation\";\n    JobCategory[\"SocialMedia\"] = \"SocialMedia\";\n    JobCategory[\"MarketResearch\"] = \"MarketResearch\";\n    // Legal Categories\n    JobCategory[\"ContractReview\"] = \"ContractReview\";\n    JobCategory[\"Compliance\"] = \"Compliance\";\n    JobCategory[\"LegalResearch\"] = \"LegalResearch\";\n    JobCategory[\"Documentation\"] = \"Documentation\";\n    // Customer Service Categories\n    JobCategory[\"CallCenter\"] = \"CallCenter\";\n    JobCategory[\"EmailSupport\"] = \"EmailSupport\";\n    JobCategory[\"ChatSupport\"] = \"ChatSupport\";\n    JobCategory[\"CustomerFeedback\"] = \"CustomerFeedback\";\n    // Other\n    JobCategory[\"Other\"] = \"Other\";\n})(JobCategory || (JobCategory = {}));\nvar OutputFormat;\n(function(OutputFormat) {\n    OutputFormat[\"PDF\"] = \"PDF\";\n    OutputFormat[\"Word\"] = \"Word\";\n    OutputFormat[\"Excel\"] = \"Excel\";\n    OutputFormat[\"PlainText\"] = \"PlainText\";\n    OutputFormat[\"JSON\"] = \"JSON\";\n    OutputFormat[\"XML\"] = \"XML\";\n    OutputFormat[\"Database\"] = \"Database\";\n    OutputFormat[\"Other\"] = \"Other\";\n})(OutputFormat || (OutputFormat = {}));\nvar JobStatus;\n(function(JobStatus) {\n    JobStatus[\"New\"] = \"New\";\n    JobStatus[\"Returned\"] = \"Returned\";\n    JobStatus[\"Assigned\"] = \"Assigned\";\n    JobStatus[\"InProgress\"] = \"In Progress\";\n    JobStatus[\"AwaitingReview\"] = \"Awaiting Review\";\n    JobStatus[\"Released\"] = \"Released\";\n    JobStatus[\"Closed\"] = \"Closed\";\n})(JobStatus || (JobStatus = {}));\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"User\"] = \"User\";\n    UserRole[\"Admin\"] = \"Admin\";\n    UserRole[\"Supervisor\"] = \"Supervisor\";\n    UserRole[\"Staff\"] = \"Staff\";\n})(UserRole || (UserRole = {}));\nvar JobHistoryAction;\n(function(JobHistoryAction) {\n    JobHistoryAction[\"Created\"] = \"Created\";\n    JobHistoryAction[\"Assigned\"] = \"Assigned\";\n    JobHistoryAction[\"Started\"] = \"Started\";\n    JobHistoryAction[\"Completed\"] = \"Completed\";\n    JobHistoryAction[\"Reviewed\"] = \"Reviewed\";\n    JobHistoryAction[\"Released\"] = \"Released\";\n    JobHistoryAction[\"Satisfied\"] = \"Satisfied\";\n    JobHistoryAction[\"Returned\"] = \"Returned\";\n    JobHistoryAction[\"TimeLogged\"] = \"TimeLogged\";\n    JobHistoryAction[\"CommentAdded\"] = \"CommentAdded\";\n    JobHistoryAction[\"StatusChanged\"] = \"StatusChanged\";\n})(JobHistoryAction || (JobHistoryAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/models.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: () => (/* binding */ getRoleBasedStatusDisplay),\n/* harmony export */   getStatusColors: () => (/* binding */ getStatusColors),\n/* harmony export */   getStatusTailwindClasses: () => (/* binding */ getStatusTailwindClasses)\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Enhanced debug logging to see what we're receiving\n    console.log(\"\\uD83D\\uDD0D Status Display Debug:\", {\n        originalStatus: status,\n        userRole,\n        statusType: typeof status,\n        userRoleType: typeof userRole\n    });\n    // Check for null/undefined status\n    if (!status) {\n        console.warn(\"⚠️ Received null or undefined status\");\n        return \"Unknown\";\n    }\n    // Normalize status to handle both backend enum names and frontend display names\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\").toLowerCase();\n    console.log(\"\\uD83D\\uDCDD Normalized status:\", normalizedStatus, \"Original:\", status);\n    let result = status; // Default fallback\n    // Log which case we're entering\n    console.log(`🔄 Processing for role: ${userRole}`);\n    switch(userRole){\n        case \"user\":\n            console.log(`👤 User role case, normalized status: ${normalizedStatus}`);\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"Submitted\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"released\":\n                    result = \"Completed\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    console.warn(`⚠️ Unrecognized status for User role: ${normalizedStatus} (original: ${status})`);\n                    result = status;\n            }\n            break;\n        case \"supervisor\":\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting My Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"staff\":\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned to Me\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"admin\":\n            // Admin sees the system status names\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        default:\n            // Default to system status names\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n    }\n    console.log(\"✅ Final result:\", {\n        originalStatus: status,\n        userRole,\n        normalizedStatus,\n        result\n    });\n    return result;\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/statusDisplay.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"927152f9cf66\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhZmZpbmdhcHAtd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83NzkzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTI3MTUyZjljZjY2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/jobs/[id]/page.tsx":
/*!************************************!*\
  !*** ./src/app/jobs/[id]/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\jobs\[id]\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staff Hall\",\n    description: \"A modern staffing solution\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTUE7QUFIZ0I7QUFLZixNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULCtKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1N0YWZmIEhhbGwnLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuIHN0YWZmaW5nIHNvbHV0aW9uJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2Zhdmljb24uaWNvP2VhNzAiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fjobs%2F%5Bid%5D%2Fpage&page=%2Fjobs%2F%5Bid%5D%2Fpage&appPaths=%2Fjobs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fjobs%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();