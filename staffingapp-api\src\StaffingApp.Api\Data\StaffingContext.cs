using Microsoft.EntityFrameworkCore;
using StaffingApp.Api.Models;

namespace StaffingApp.Api.Data
{
    public class StaffingContext : DbContext
    {
        public StaffingContext(DbContextOptions<StaffingContext> options) : base(options)
        {
        }

        public DbSet<Client> Clients { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Job> Jobs { get; set; }
        public DbSet<JobTask> JobTasks { get; set; }
        public DbSet<Attachment> Attachments { get; set; }
        public DbSet<Staff> Staff { get; set; }
        public DbSet<PaymentRate> PaymentRates { get; set; }
        public DbSet<HoursPurchase> HoursPurchases { get; set; }
        public DbSet<JobComment> JobComments { get; set; }
    
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure Client
            modelBuilder.Entity<Client>()
                .HasMany(c => c.Users)
                .WithOne(u => u.Client)
                .HasForeignKey(u => u.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Jobs
            modelBuilder.Entity<Job>()
                .HasOne(j => j.Client)
                .WithMany(c => c.Jobs)
                .HasForeignKey(j => j.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Job>()
                .HasOne(j => j.CreatedBy)
                .WithMany()
                .HasForeignKey(j => j.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure JobTasks
            modelBuilder.Entity<JobTask>()
                .HasOne(t => t.Job)
                .WithMany(j => j.Tasks)
                .HasForeignKey(t => t.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure PaymentRate
            modelBuilder.Entity<PaymentRate>()
                .HasOne(r => r.CreatedBy)
                .WithMany()
                .HasForeignKey(r => r.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PaymentRate>()
                .Property(p => p.Currency)
                .IsRequired();

            modelBuilder.Entity<PaymentRate>()
                .Property(p => p.RatePerHour)
                .HasColumnType("decimal(18,2)");

            modelBuilder.Entity<HoursPurchase>()
                .Property(h => h.Hours)
                .HasColumnType("decimal(18,2)");

            modelBuilder.Entity<HoursPurchase>()
                .Property(h => h.Amount)
                .HasColumnType("decimal(18,2)");

            modelBuilder.Entity<HoursPurchase>()
                .HasOne(h => h.PaymentRate)
                .WithMany()
                .HasForeignKey(h => h.PaymentRateId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<HoursPurchase>()
                .HasOne(h => h.User)
                .WithMany()
                .HasForeignKey(h => h.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<HoursPurchase>()
                .HasOne(h => h.Processor)
                .WithMany()
                .HasForeignKey(h => h.ProcessedBy)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure JobComments
            modelBuilder.Entity<JobComment>()
                .HasOne(c => c.Job)
                .WithMany(j => j.Comments)
                .HasForeignKey(c => c.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<JobComment>()
                .HasOne(c => c.User)
                .WithMany()
                .HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            base.OnModelCreating(modelBuilder);
        }
    }
}