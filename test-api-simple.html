<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple API Test - Staff Hall</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #059669;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple API Test</h1>
        
        <button class="button" onclick="testAPI()">Test API Connection</button>
        <button class="button" onclick="createStaff()">Create Staff User</button>
        <button class="button" onclick="createSupervisor()">Create Supervisor User</button>
        <button class="button" onclick="listUsers()">List All Users</button>
        <button class="button" onclick="clearResults()">Clear</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';

        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').textContent = '';
        }

        async function testAPI() {
            log('Testing API connection...');
            try {
                const response = await fetch(`${API_BASE}/auth/test-users`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API is working! Found ${data.length} users`);
                    data.forEach(user => {
                        log(`  - ${user.firstName} ${user.lastName} (${user.email}) - Role: ${user.role}`);
                    });
                } else {
                    log(`❌ API error: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`❌ Connection error: ${error.message}`);
            }
        }

        async function createStaff() {
            log('Creating staff user...');
            try {
                const response = await fetch(`${API_BASE}/auth/create-staff`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.text();
                if (response.ok) {
                    log('✅ Staff user created successfully');
                    log('Response: ' + data);
                } else {
                    log(`❌ Failed to create staff: ${response.status}`);
                    log('Error: ' + data);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`);
            }
        }

        async function createSupervisor() {
            log('Creating supervisor user...');
            try {
                const response = await fetch(`${API_BASE}/auth/create-supervisor`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.text();
                if (response.ok) {
                    log('✅ Supervisor user created successfully');
                    log('Response: ' + data);
                } else {
                    log(`❌ Failed to create supervisor: ${response.status}`);
                    log('Error: ' + data);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`);
            }
        }

        async function listUsers() {
            log('Fetching all users...');
            try {
                const response = await fetch(`${API_BASE}/auth/test-users`);
                if (response.ok) {
                    const users = await response.json();
                    log(`📋 Found ${users.length} users:`);
                    users.forEach(user => {
                        log(`  ID: ${user.id}, Name: ${user.firstName} ${user.lastName}`);
                        log(`  Email: ${user.email}, Role: ${user.role}, ClientId: ${user.clientId}`);
                        log('  ---');
                    });
                } else {
                    log(`❌ Failed to fetch users: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`);
            }
        }

        // Auto-test on load
        window.onload = function() {
            log('Page loaded. Click "Test API Connection" to start.');
        };
    </script>
</body>
</html>
