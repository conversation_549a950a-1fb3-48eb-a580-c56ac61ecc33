import axios from 'axios';
import { JobHistory, LogTimeRequest, ReviewJobRequest, TimeEntryApprovalRequest } from '@/types/models';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

// Get job history
export const getJobHistory = async (jobId: number): Promise<JobHistory[]> => {
    const token = sessionStorage.getItem('token');
    if (!token) {
        throw new Error('Authentication required');
    }

    const response = await axios.get(`${API_URL}/api/jobs/${jobId}/history`, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });

    return response.data;
};

// Log time for a job (staff only)
export const logTime = async (jobId: number, request: LogTimeRequest): Promise<void> => {
    const token = sessionStorage.getItem('token');
    if (!token) {
        throw new Error('Authentication required');
    }

    await axios.post(`${API_URL}/api/jobs/${jobId}/log-time`, request, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
};

// Review job with feedback (supervisor only)
export const reviewJobWithFeedback = async (jobId: number, request: ReviewJobRequest): Promise<void> => {
    const token = sessionStorage.getItem('token');
    if (!token) {
        throw new Error('Authentication required');
    }

    await axios.put(`${API_URL}/api/jobs/${jobId}/review-with-feedback`, request, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
};

// Approve or reject time entry (supervisor only)
export const approveTimeEntry = async (jobId: number, taskId: number, request: TimeEntryApprovalRequest): Promise<void> => {
    const token = sessionStorage.getItem('token');
    if (!token) {
        throw new Error('Authentication required');
    }

    await axios.put(`${API_URL}/api/jobs/${jobId}/tasks/${taskId}/approve`, request, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
};
